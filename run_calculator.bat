@echo off
echo تشغيل حاسبة معاش المتقاعدين...
echo Running Pension Calculator...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed
    pause
    exit /b 1
)

REM تشغيل التطبيق الأساسي
echo تشغيل النسخة الأساسية...
echo Running basic version...
python pension_calculator.py

REM في حالة فشل التطبيق الأساسي، جرب النسخة المحسنة
if errorlevel 1 (
    echo.
    echo تشغيل النسخة المحسنة...
    echo Running enhanced version...
    python enhanced_pension_calculator.py
)

echo.
echo انتهى التشغيل
echo Execution completed
pause
