# حاسبة معاش المتقاعدين
## Pension Calculator Application

تطبيق Python لحساب الزيادة في معاشات المتقاعدين مع واجهة مستخدم باللغة العربية.

### المميزات
- واجهة مستخدم باللغة العربية
- حساب دقيق للمعاش الجديد بعد الزيادة
- عرض تفصيلي لجميع المكونات
- تصميم محسن وسهل الاستخدام

### المعادلات المستخدمة
1. **الخام قبل التثمين** = المعاش الصافي الحالي + نسبة IRG + CNAS
2. **قيمة الزيادة** = الخام قبل التثمين × (نسبة الزيادة ÷ 100)
3. **الخام الجديد** = الخام قبل التثمين + قيمة الزيادة
4. **CNAS الجديد** = الخام الجديد × 2%
5. **الخام الخاضع للضريبة الجديد** = الخام الجديد - CNAS الجديد
6. **IRG الجديد** = الخام الخاضع للضريبة الجديد × نسبة IRG الأصلية
7. **المعاش الصافي الجديد** = الخام الجديد - CNAS الجديد - IRG الجديد

### متطلبات التشغيل
- Python 3.6 أو أحدث
- tkinter (مدمج مع Python)

### طريقة التشغيل

#### الطريقة الأولى (Windows):
```bash
run_calculator.bat
```

#### الطريقة الثانية (النسخة الأساسية):
```bash
python pension_calculator.py
```

#### الطريقة الثالثة (النسخة المحسنة):
```bash
python enhanced_pension_calculator.py
```

### طريقة الاستخدام
1. أدخل البيانات المطلوبة في الحقول
2. اضغط على "حساب المعاش الجديد"
3. ستظهر النتائج في الجدول أسفل الشاشة

### البيانات المطلوبة
- **المعاش الصافي الحالي**: المبلغ الحالي للمعاش
- **نسبة IRG المحسوبة**: قيمة ضريبة الدخل الحالية
- **الخام الخاضع للضريبة**: المبلغ الخاضع للضريبة حالياً
- **CNAS (2%)**: قيمة الضمان الاجتماعي الحالية
- **نسبة الزيادة (%)**: النسبة المئوية للزيادة المطلوبة

### النتائج المعروضة
- الخام قبل التثمين
- نسبة التثمين
- الخام الجديد بعد التثمين
- CNAS الجديد (2%)
- الخام الخاضع للضريبة الجديد
- نسبة IRG المحسوبة الجديدة
- **المعاش الصافي الجديد** (مميز بلون أخضر)
- **قيمة الزيادة** (مميز بلون برتقالي)

### الملفات المتوفرة
- `pension_calculator.py` - النسخة الأساسية
- `enhanced_pension_calculator.py` - النسخة المحسنة مع مميزات إضافية
- `run_calculator.bat` - ملف تشغيل سريع لنظام Windows
- `config.json` - ملف الإعدادات
- `requirements.txt` - متطلبات التشغيل

### المميزات الإضافية في النسخة المحسنة
- واجهة مستخدم محسنة مع شريط قوائم
- حفظ وتحميل البيانات
- تاريخ الحسابات
- إحصائيات سريعة
- مقارنة النتائج
- تصدير النتائج
- التحقق من صحة البيانات
- شريط تمرير للواجهة الطويلة

### ملاحظات
- يدعم التطبيق الأرقام العشرية
- يمكن استخدام الفاصلة أو النقطة للأرقام العشرية
- يتم عرض النتائج بتنسيق واضح مع فواصل الآلاف
- يحفظ التطبيق تاريخ آخر 50 حساب
- يمكن تخصيص الإعدادات من ملف config.json

---

## English Version

A Python application for calculating pension increases with Arabic user interface.

### Features
- Arabic language user interface
- Accurate calculation of new pension after increase
- Detailed display of all components
- Enhanced and user-friendly design

### How to Run
```bash
python pension_calculator.py
```

### Requirements
- Python 3.6 or newer
- tkinter (built-in with Python)
