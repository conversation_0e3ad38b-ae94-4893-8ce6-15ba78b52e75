#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حاسبة معاش المتقاعدين المبسطة
Simple Pension Calculator
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import datetime
import csv

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.units import inch
    from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from bidi.algorithm import get_display
    import arabic_reshaper
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("تحذير: بعض المكتبات غير مثبتة. ميزة PDF قد تكون محدودة.")
    print("لتثبيت المكتبات: pip install reportlab python-bidi arabic-reshaper")

class SimplePensionCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("حاسبة معاش المتقاعدين - Pension Calculator")
        self.root.geometry("800x600")
        self.root.configure(bg='#2c3e50')

        # الخطوط
        self.arabic_font = ('Arial Unicode MS', 12)
        self.title_font = ('Arial Unicode MS', 16, 'bold')
        self.result_font = ('Arial Unicode MS', 13, 'bold')

        # متغيرات البيانات
        self.history = []
        self.entry_vars = {}

        self.create_widgets()
        self.create_menu()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي قابل للتمرير
        canvas = tk.Canvas(self.root, bg='#2c3e50')
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#2c3e50')

        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # العنوان
        title_label = tk.Label(scrollable_frame, text="حاسبة معاش المتقاعدين",
                              font=self.title_font, bg='#2c3e50', fg='white')
        title_label.pack(pady=20)

        # إطار الإدخال
        input_frame = tk.Frame(scrollable_frame, bg='#34495e', relief='raised', bd=2)
        input_frame.pack(padx=20, pady=10, fill='x')

        # حقول الإدخال
        fields = [
            ("current_pension", "المعاش الحالي (دج)"),
            ("increase_amount", "قيمة الزيادة (دج)"),
            ("current_gross", "الخام الحالي (دج)"),
            ("new_gross", "الخام الجديد (دج)")
        ]

        for var_name, label_text in fields:
            frame = tk.Frame(input_frame, bg='#34495e')
            frame.pack(fill='x', padx=10, pady=5)

            label = tk.Label(frame, text=label_text, font=self.arabic_font,
                           bg='#34495e', fg='white', width=20, anchor='e')
            label.pack(side='left')

            var = tk.StringVar()
            entry = tk.Entry(frame, textvariable=var, font=self.arabic_font, width=15)
            entry.pack(side='right', padx=5)

            self.entry_vars[var_name] = var

        # أزرار العمليات
        button_frame = tk.Frame(scrollable_frame, bg='#2c3e50')
        button_frame.pack(pady=20)

        buttons = [
            ("حساب المعاش", self.calculate_pension, '#3498db'),
            ("حفظ النتائج", self.save_results, '#2ecc71'),
            ("طباعة PDF", self.print_pdf, '#e74c3c'),
            ("مسح البيانات", self.clear_data, '#95a5a6')
        ]

        for text, command, color in buttons:
            btn = tk.Button(button_frame, text=text, font=self.arabic_font,
                          bg=color, fg='white', command=command,
                          padx=20, pady=10, relief='raised', bd=3)
            btn.pack(side='left', padx=5)

        # إطار النتائج
        self.result_frame = tk.Frame(scrollable_frame, bg='#34495e', relief='raised', bd=2)
        self.result_frame.pack(padx=20, pady=10, fill='both', expand=True)

        result_title = tk.Label(self.result_frame, text="النتائج",
                               font=self.title_font, bg='#34495e', fg='white')
        result_title.pack(pady=10)

        self.result_text = tk.Text(self.result_frame, font=self.result_font,
                                  bg='white', fg='black', height=15, wrap='word')
        self.result_text.pack(padx=10, pady=10, fill='both', expand=True)

    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="حفظ النتائج", command=self.save_results)
        file_menu.add_command(label="طباعة PDF", command=self.print_pdf)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)

    def calculate_pension(self):
        """حساب المعاش"""
        try:
            # الحصول على القيم
            current_pension = float(self.entry_vars["current_pension"].get() or 0)
            increase_amount = float(self.entry_vars["increase_amount"].get() or 0)
            current_gross = float(self.entry_vars["current_gross"].get() or 0)
            new_gross = float(self.entry_vars["new_gross"].get() or 0)

            # حساب القيم الجديدة
            new_pension = current_pension + increase_amount
            increase_rate = (increase_amount / current_pension * 100) if current_pension > 0 else 0

            # حساب الضرائب والاقتطاعات
            current_cnas = current_gross * 0.02
            new_cnas = new_gross * 0.02

            current_irg = max(0, (current_gross - 30000) * 0.20) if current_gross > 30000 else 0
            new_irg = max(0, (new_gross - 30000) * 0.20) if new_gross > 30000 else 0

            current_net = current_gross - current_cnas - current_irg
            new_net = new_gross - new_cnas - new_irg

            # إعداد النتائج
            result = {
                'date': datetime.datetime.now().isoformat(),
                'calculations': {
                    'current': {
                        'pension': current_pension,
                        'gross': current_gross,
                        'cnas': current_cnas,
                        'irg': current_irg,
                        'net': current_net
                    },
                    'new': {
                        'pension': new_pension,
                        'gross': new_gross,
                        'cnas': new_cnas,
                        'irg': new_irg,
                        'net': new_net
                    },
                    'increase': {
                        'amount': increase_amount,
                        'rate': increase_rate,
                        'net_difference': new_net - current_net
                    }
                }
            }

            # حفظ في التاريخ
            self.history.append(result)
            if len(self.history) > 50:
                self.history.pop(0)

            # عرض النتائج
            self.display_results(result)

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة في جميع الحقول")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في الحساب: {str(e)}")

    def display_results(self, result):
        """عرض النتائج"""
        calc = result['calculations']

        text = f"""
═══════════════════════════════════════════════════════════════
                            نتائج حساب المعاش
═══════════════════════════════════════════════════════════════

📅 تاريخ الحساب: {datetime.datetime.fromisoformat(result['date']).strftime('%Y-%m-%d %H:%M')}

💰 المعاش:
   • المعاش الحالي: {calc['current']['pension']:,.2f} دج
   • المعاش الجديد: {calc['new']['pension']:,.2f} دج
   • قيمة الزيادة: {calc['increase']['amount']:,.2f} دج
   • نسبة الزيادة: {calc['increase']['rate']:.2f}%

💼 الراتب الخام:
   • الخام الحالي: {calc['current']['gross']:,.2f} دج
   • الخام الجديد: {calc['new']['gross']:,.2f} دج
   • الفرق: {calc['new']['gross'] - calc['current']['gross']:,.2f} دج

🏥 CNAS (2%):
   • الحالي: {calc['current']['cnas']:,.2f} دج
   • الجديد: {calc['new']['cnas']:,.2f} دج
   • الفرق: {calc['new']['cnas'] - calc['current']['cnas']:,.2f} دج

💸 IRG (الضريبة):
   • الحالي: {calc['current']['irg']:,.2f} دج
   • الجديد: {calc['new']['irg']:,.2f} دج
   • الفرق: {calc['new']['irg'] - calc['current']['irg']:,.2f} دج

💵 الصافي:
   • الصافي الحالي: {calc['current']['net']:,.2f} دج
   • الصافي الجديد: {calc['new']['net']:,.2f} دج
   • الزيادة الصافية: {calc['increase']['net_difference']:,.2f} دج

📊 الزيادة السنوية: {calc['increase']['net_difference'] * 12:,.2f} دج

═══════════════════════════════════════════════════════════════
"""

        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(1.0, text)

    def save_results(self):
        """حفظ النتائج"""
        if not self.history:
            messagebox.showwarning("تحذير", "لا توجد نتائج لحفظها")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ النتائج"
            )

            if filename:
                if filename.lower().endswith('.csv'):
                    self.save_csv(filename)
                else:
                    self.save_json(filename)
                messagebox.showinfo("نجح", f"تم حفظ النتائج في:\n{filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ النتائج: {str(e)}")

    def save_json(self, filename):
        """حفظ JSON"""
        data = {
            'timestamp': datetime.datetime.now().isoformat(),
            'application': 'حاسبة معاش المتقاعدين',
            'results': self.history
        }
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def save_csv(self, filename):
        """حفظ CSV"""
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['التاريخ', 'المعاش الحالي', 'المعاش الجديد', 'الزيادة', 'النسبة%', 'الصافي الحالي', 'الصافي الجديد'])

            for entry in self.history:
                calc = entry['calculations']
                date_str = datetime.datetime.fromisoformat(entry['date']).strftime('%Y-%m-%d %H:%M')
                writer.writerow([
                    date_str,
                    f"{calc['current']['pension']:,.2f}",
                    f"{calc['new']['pension']:,.2f}",
                    f"{calc['increase']['amount']:,.2f}",
                    f"{calc['increase']['rate']:.2f}",
                    f"{calc['current']['net']:,.2f}",
                    f"{calc['new']['net']:,.2f}"
                ])

    def print_pdf(self):
        """طباعة PDF مع دعم العربية RTL"""
        if not self.history:
            messagebox.showwarning("تحذير", "لا توجد نتائج لطباعتها")
            return

        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة reportlab غير متاحة")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ تقرير PDF"
            )

            if filename:
                self.create_pdf_report(filename)
                messagebox.showinfo("نجح", f"تم إنشاء تقرير PDF:\n{filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء PDF: {str(e)}")

    def create_pdf_report(self, filename):
        """إنشاء تقرير PDF مع دعم العربية RTL محسن"""
        doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
        story = []

        # تسجيل خط عربي
        arabic_font = self.register_arabic_font()

        # دالة معالجة النص العربي RTL
        def format_arabic_rtl(text):
            try:
                # إعادة تشكيل النص العربي
                reshaped_text = arabic_reshaper.reshape(text)
                # تطبيق خوارزمية BiDi للاتجاه من اليمين لليسار
                bidi_text = get_display(reshaped_text)
                return bidi_text
            except:
                return text

        # أنماط النصوص مع محاذاة RTL
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=getSampleStyleSheet()['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            fontName=arabic_font,
            wordWrap='RTL'
        )

        info_style = ParagraphStyle(
            'ArabicInfo',
            parent=getSampleStyleSheet()['Normal'],
            fontSize=12,
            alignment=TA_RIGHT,
            fontName=arabic_font,
            wordWrap='RTL'
        )

        subtitle_style = ParagraphStyle(
            'ArabicSubtitle',
            parent=getSampleStyleSheet()['Heading2'],
            fontSize=14,
            spaceAfter=15,
            alignment=TA_RIGHT,
            fontName=arabic_font,
            wordWrap='RTL'
        )

        # العنوان الرئيسي
        title_text = format_arabic_rtl("تقرير حساب معاش المتقاعدين")
        story.append(Paragraph(title_text, title_style))
        story.append(Spacer(1, 12))

        # معلومات التقرير
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        date_text = format_arabic_rtl(f"تاريخ التقرير: {current_time}")
        count_text = format_arabic_rtl(f"عدد الحسابات: {len(self.history)}")

        story.append(Paragraph(date_text, info_style))
        story.append(Paragraph(count_text, info_style))
        story.append(Spacer(1, 20))

        # الحساب الأخير
        if self.history:
            latest = self.history[-1]['calculations']

            # عنوان القسم
            subtitle_text = format_arabic_rtl("الحساب الأخير")
            story.append(Paragraph(subtitle_text, subtitle_style))

            # جدول النتائج مع النص العربي RTL
            data = [
                # العناوين من اليمين لليسار
                [
                    format_arabic_rtl("البيان"),
                    format_arabic_rtl("القيمة الحالية"),
                    format_arabic_rtl("القيمة الجديدة"),
                    format_arabic_rtl("الفرق")
                ],
                [
                    format_arabic_rtl("المعاش"),
                    f"{latest['current']['pension']:,.2f} دج",
                    f"{latest['new']['pension']:,.2f} دج",
                    f"{latest['new']['pension'] - latest['current']['pension']:,.2f} دج"
                ],
                [
                    format_arabic_rtl("الراتب الخام"),
                    f"{latest['current']['gross']:,.2f} دج",
                    f"{latest['new']['gross']:,.2f} دج",
                    f"{latest['new']['gross'] - latest['current']['gross']:,.2f} دج"
                ],
                [
                    "CNAS (2%)",
                    f"{latest['current']['cnas']:,.2f} دج",
                    f"{latest['new']['cnas']:,.2f} دج",
                    f"{latest['new']['cnas'] - latest['current']['cnas']:,.2f} دج"
                ],
                [
                    format_arabic_rtl("ضريبة IRG"),
                    f"{latest['current']['irg']:,.2f} دج",
                    f"{latest['new']['irg']:,.2f} دج",
                    f"{latest['new']['irg'] - latest['current']['irg']:,.2f} دج"
                ],
                [
                    format_arabic_rtl("الصافي"),
                    f"{latest['current']['net']:,.2f} دج",
                    f"{latest['new']['net']:,.2f} دج",
                    f"{latest['new']['net'] - latest['current']['net']:,.2f} دج"
                ]
            ]

            # إنشاء الجدول مع محاذاة RTL
            table = Table(data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 1.5*inch])
            table.setStyle(TableStyle([
                # تنسيق العناوين
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('FONTNAME', (0, 0), (-1, -1), arabic_font),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

                # تنسيق البيانات
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),  # العمود الأول (البيان) محاذاة يمين
                ('ALIGN', (1, 0), (-1, -1), 'CENTER'),  # باقي الأعمدة وسط
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
            ]))

            story.append(table)
            story.append(Spacer(1, 20))

            # معلومات إضافية مع محاذاة RTL
            additional_info = [
                f"قيمة الزيادة: {latest['increase']['amount']:,.2f} دج",
                f"نسبة الزيادة: {latest['increase']['rate']:.2f}%",
                f"الزيادة الشهرية الصافية: {latest['increase']['net_difference']:,.2f} دج",
                f"الزيادة السنوية الصافية: {latest['increase']['net_difference'] * 12:,.2f} دج"
            ]

            for info in additional_info:
                formatted_info = format_arabic_rtl(info)
                story.append(Paragraph(formatted_info, info_style))
                story.append(Spacer(1, 6))

        # بناء المستند
        doc.build(story)

    def register_arabic_font(self):
        """تسجيل خط عربي مناسب"""
        try:
            # محاولة تسجيل خطوط عربية مختلفة
            fonts_to_try = [
                ('C:/Windows/Fonts/arial.ttf', 'Arial'),
                ('C:/Windows/Fonts/tahoma.ttf', 'Tahoma'),
                ('arial.ttf', 'Arial'),
                ('tahoma.ttf', 'Tahoma')
            ]

            for font_path, font_name in fonts_to_try:
                try:
                    pdfmetrics.registerFont(TTFont(font_name, font_path))
                    print(f"تم تسجيل الخط العربي: {font_name}")
                    return font_name
                except:
                    continue

            print("لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
            return 'Helvetica'

        except Exception as e:
            print(f"خطأ في تسجيل الخط: {e}")
            return 'Helvetica'

    def clear_data(self):
        """مسح البيانات"""
        for var in self.entry_vars.values():
            var.set("")
        self.result_text.delete(1.0, tk.END)

def main():
    root = tk.Tk()
    app = SimplePensionCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
