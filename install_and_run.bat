@echo off
echo ===============================================
echo تثبيت وتشغيل حاسبة معاش المتقاعدين
echo Installing and Running Pension Calculator
echo ===============================================
echo.

REM التحقق من وجود Python
echo التحقق من وجود Python...
echo Checking for Python...

python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo Python غير مثبت. يرجى تثبيت Python أولاً من:
    echo Python is not installed. Please install Python first from:
    echo https://www.python.org/downloads/
    echo.
    echo أو يمكنك تثبيته من Microsoft Store
    echo Or you can install it from Microsoft Store
    echo.
    pause
    exit /b 1
)

echo Python مثبت بنجاح!
echo Python is installed successfully!
echo.

REM تشغيل اختبار الحسابات
echo تشغيل اختبار الحسابات...
echo Running calculation test...
python test_calculations.py
echo.

REM تشغيل التطبيق
echo تشغيل التطبيق...
echo Running application...
echo.
echo اختر النسخة المطلوبة:
echo Choose the version you want:
echo 1. النسخة الأساسية (Basic Version)
echo 2. النسخة المحسنة (Enhanced Version)
echo.

set /p choice="أدخل اختيارك (1 أو 2): "

if "%choice%"=="1" (
    echo تشغيل النسخة الأساسية...
    python pension_calculator.py
) else if "%choice%"=="2" (
    echo تشغيل النسخة المحسنة...
    python enhanced_pension_calculator.py
) else (
    echo اختيار غير صحيح، تشغيل النسخة الأساسية...
    python pension_calculator.py
)

echo.
echo انتهى التشغيل
echo Execution completed
pause
