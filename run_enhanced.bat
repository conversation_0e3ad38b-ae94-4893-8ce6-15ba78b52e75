@echo off
chcp 65001 >nul
echo ===============================================
echo 🚀 تشغيل حاسبة معاش المتقاعدين المحسنة
echo    Enhanced Pension Calculator Launcher
echo ===============================================
echo.

echo 📊 تشغيل اختبار الحسابات أولاً...
echo Running calculation test first...
echo.

python test_calculations.py
if errorlevel 1 (
    echo.
    echo ⚠️ فشل في تشغيل اختبار الحسابات
    echo Failed to run calculation test
    pause
    exit /b 1
)

echo.
echo ✅ اختبار الحسابات مكتمل!
echo Calculation test completed!
echo.

echo 🎯 اختر النسخة المطلوبة:
echo Choose your preferred version:
echo.
echo 1. النسخة الأساسية المحسنة (Basic Enhanced)
echo    - واجهة بسيطة مع تكبير
echo    - مفاتيح اختصار: Ctrl +/- للتكبير
echo.
echo 2. النسخة المتقدمة (Advanced)
echo    - واجهة متقدمة مع قوائم
echo    - تحكم كامل في الخط والعرض
echo.
echo 3. كلا النسختين معاً (Both)
echo    - فتح النسختين للمقارنة
echo.

set /p choice="أدخل اختيارك (1, 2, أو 3): "

if "%choice%"=="1" (
    echo.
    echo 🖥️ تشغيل النسخة الأساسية المحسنة...
    echo Starting Basic Enhanced Version...
    echo.
    echo 💡 نصائح الاستخدام:
    echo - Ctrl + Plus: تكبير
    echo - Ctrl + Minus: تصغير  
    echo - Ctrl + 0: إعادة تعيين
    echo - F11: ملء الشاشة
    echo.
    python pension_calculator.py
    
) else if "%choice%"=="2" (
    echo.
    echo 🖥️ تشغيل النسخة المتقدمة...
    echo Starting Advanced Version...
    echo.
    echo 💡 نصائح الاستخدام:
    echo - قائمة "عرض" للتحكم في الخط
    echo - قائمة "ملف" للحفظ والتحميل
    echo - قائمة "أدوات" للمميزات الإضافية
    echo.
    python enhanced_pension_calculator.py
    
) else if "%choice%"=="3" (
    echo.
    echo 🖥️ تشغيل كلا النسختين...
    echo Starting Both Versions...
    echo.
    start python pension_calculator.py
    timeout /t 2 /nobreak >nul
    start python enhanced_pension_calculator.py
    echo.
    echo ✅ تم فتح النسختين!
    echo Both versions are now running!
    
) else (
    echo.
    echo ❌ اختيار غير صحيح، تشغيل النسخة المتقدمة...
    echo Invalid choice, starting Advanced Version...
    python enhanced_pension_calculator.py
)

echo.
echo 🎉 انتهى التشغيل
echo Execution completed
echo.
pause
