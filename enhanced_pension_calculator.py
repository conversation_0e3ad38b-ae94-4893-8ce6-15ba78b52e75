#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق حساب معاش المتقاعدين المحسن
Enhanced Pension Calculator Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import datetime
import os

class EnhancedPensionCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("حاسبة معاش المتقاعدين المحسنة - Enhanced Pension Calculator")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')
        
        # تكوين الخطوط
        self.arabic_font = ('Arial Unicode MS', 11)
        self.title_font = ('Arial Unicode MS', 18, 'bold')
        self.result_font = ('Arial Unicode MS', 12, 'bold')
        self.small_font = ('Arial Unicode MS', 9)
        
        # متغيرات الحفظ والاستعادة
        self.history = []
        
        self.create_widgets()
        self.create_menu()
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="حفظ البيانات", command=self.save_data)
        file_menu.add_command(label="تحميل البيانات", command=self.load_data)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير النتائج", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="مقارنة النتائج", command=self.show_comparison)
        tools_menu.add_command(label="تاريخ الحسابات", command=self.show_history)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
        
    def create_widgets(self):
        # إطار رئيسي مع شريط تمرير
        main_canvas = tk.Canvas(self.root, bg='#2c3e50')
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=main_canvas.yview)
        scrollable_frame = tk.Frame(main_canvas, bg='#2c3e50')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)
        
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # العنوان الرئيسي مع التاريخ
        title_frame = tk.Frame(scrollable_frame, bg='#34495e', relief='raised', bd=3)
        title_frame.pack(fill='x', padx=10, pady=10)
        
        title_label = tk.Label(title_frame, text="حاسبة الزيادة في معاشات المتقاعدين", 
                              font=self.title_font, bg='#34495e', fg='#ecf0f1')
        title_label.pack(pady=5)
        
        date_label = tk.Label(title_frame, text=f"التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}", 
                             font=self.small_font, bg='#34495e', fg='#bdc3c7')
        date_label.pack(pady=2)
        
        # إطار الإدخال المحسن
        input_frame = tk.LabelFrame(scrollable_frame, text="بيانات الإدخال", font=self.arabic_font,
                                   bg='#ecf0f1', fg='#2c3e50', relief='groove', bd=3)
        input_frame.pack(fill='x', padx=10, pady=5)
        
        # إنشاء الحقول مع التحقق من صحة البيانات
        self.create_input_fields(input_frame)
        
        # أزرار التحكم المحسنة
        self.create_control_buttons(scrollable_frame)
        
        # إطار النتائج المحسن
        self.create_enhanced_results_frame(scrollable_frame)
        
        # إطار الإحصائيات
        self.create_statistics_frame(scrollable_frame)
        
    def create_input_fields(self, parent):
        """إنشاء حقول الإدخال مع التحقق"""
        fields = [
            ("المعاش الصافي الحالي (دج):", "current_pension", "40608.00"),
            ("نسبة IRG المحسوبة (دج):", "irg_rate", "4472.00"),
            ("الخام الخاضع للضريبة (دج):", "taxable_gross", "43080.00"),
            ("CNAS 2% (دج):", "cnas", "920.00"),
            ("نسبة الزيادة (%):", "increase_rate", "15")
        ]
        
        self.entry_vars = {}
        
        for i, (label_text, var_name, default_value) in enumerate(fields):
            # إطار لكل حقل
            field_frame = tk.Frame(parent, bg='#ecf0f1')
            field_frame.grid(row=i//2, column=(i%2)*2, columnspan=2, sticky='ew', padx=5, pady=3)
            
            # التسمية
            label = tk.Label(field_frame, text=label_text, font=self.arabic_font,
                           bg='#ecf0f1', fg='#2c3e50', width=25, anchor='e')
            label.pack(side='right', padx=5)
            
            # حقل الإدخال
            var = tk.StringVar(value=default_value)
            entry = tk.Entry(field_frame, textvariable=var, font=self.arabic_font,
                           width=15, justify='center', relief='sunken', bd=2)
            entry.pack(side='right', padx=5)
            
            # ربط التحقق من البيانات
            entry.bind('<KeyRelease>', lambda e, v=var: self.validate_input(v))
            
            self.entry_vars[var_name] = var
        
        # تكوين الشبكة
        for i in range(3):
            parent.grid_columnconfigure(i, weight=1)
    
    def validate_input(self, var):
        """التحقق من صحة البيانات المدخلة"""
        try:
            value = var.get().replace(',', '')
            if value and float(value) < 0:
                var.set("0")
        except ValueError:
            pass
    
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        button_frame = tk.Frame(parent, bg='#2c3e50')
        button_frame.pack(fill='x', padx=10, pady=10)
        
        buttons = [
            ("حساب المعاش الجديد", self.calculate_pension, '#27ae60'),
            ("مسح البيانات", self.clear_data, '#e74c3c'),
            ("حفظ النتائج", self.save_results, '#3498db'),
            ("طباعة النتائج", self.print_results, '#9b59b6')
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(button_frame, text=text, font=self.arabic_font,
                          bg=color, fg='white', command=command,
                          relief='raised', bd=3, padx=20, pady=5)
            btn.pack(side='right', padx=5)
    
    def create_enhanced_results_frame(self, parent):
        """إنشاء إطار النتائج المحسن"""
        self.result_frame = tk.LabelFrame(parent, text="النتائج التفصيلية", font=self.arabic_font,
                                         bg='#ecf0f1', fg='#2c3e50', relief='groove', bd=3)
        self.result_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إنشاء جدول النتائج المحسن
        self.create_enhanced_results_table()
    
    def create_enhanced_results_table(self):
        """إنشاء جدول النتائج المحسن"""
        # إطار للجدول مع شريط تمرير
        table_container = tk.Frame(self.result_frame, bg='#ecf0f1')
        table_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # عناوين الأعمدة
        headers = ["البيان", "القيمة الحالية", "القيمة الجديدة", "الفرق"]
        colors = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12']
        
        for i, (header, color) in enumerate(zip(headers, colors)):
            label = tk.Label(table_container, text=header, font=self.result_font,
                           bg=color, fg='white', relief='raised', bd=2, pady=5)
            label.grid(row=0, column=i, sticky='ew', padx=1, pady=1)
        
        # صفوف البيانات
        self.result_labels = {}
        result_items = [
            ("المعاش الصافي", "net_pension"),
            ("الخام الإجمالي", "gross_total"),
            ("CNAS (2%)", "cnas_amount"),
            ("IRG المحسوب", "irg_amount"),
            ("الخام الخاضع للضريبة", "taxable_amount"),
            ("قيمة الزيادة", "increase_value"),
            ("نسبة الزيادة", "increase_percent")
        ]
        
        for i, (label_text, key) in enumerate(result_items, 1):
            # عمود البيان
            tk.Label(table_container, text=label_text, font=self.arabic_font,
                    bg='#bdc3c7', relief='raised', bd=1, pady=3).grid(row=i, column=0, 
                    sticky='ew', padx=1, pady=1)
            
            # الأعمدة الأخرى
            for j in range(1, 4):
                value_label = tk.Label(table_container, text="0.00", font=self.arabic_font,
                                     bg='white', relief='sunken', bd=1, pady=3)
                value_label.grid(row=i, column=j, sticky='ew', padx=1, pady=1)
                self.result_labels[f"{key}_{j}"] = value_label
        
        # تكوين الأعمدة
        for i in range(4):
            table_container.grid_columnconfigure(i, weight=1)
    
    def create_statistics_frame(self, parent):
        """إنشاء إطار الإحصائيات"""
        stats_frame = tk.LabelFrame(parent, text="إحصائيات سريعة", font=self.arabic_font,
                                   bg='#ecf0f1', fg='#2c3e50', relief='groove', bd=3)
        stats_frame.pack(fill='x', padx=10, pady=5)
        
        self.stats_labels = {}
        stats_items = [
            ("إجمالي الزيادة الشهرية:", "monthly_increase"),
            ("إجمالي الزيادة السنوية:", "yearly_increase"),
            ("نسبة الزيادة الفعلية:", "effective_rate")
        ]
        
        for i, (text, key) in enumerate(stats_items):
            frame = tk.Frame(stats_frame, bg='#ecf0f1')
            frame.pack(side='left', expand=True, fill='x', padx=10, pady=5)
            
            tk.Label(frame, text=text, font=self.arabic_font,
                    bg='#ecf0f1', fg='#2c3e50').pack(side='top')
            
            value_label = tk.Label(frame, text="0.00 دج", font=self.result_font,
                                 bg='#34495e', fg='white', relief='raised', bd=2, pady=5)
            value_label.pack(side='top', fill='x')
            self.stats_labels[key] = value_label
    
    def calculate_pension(self):
        """حساب المعاش الجديد مع التحسينات"""
        try:
            # قراءة البيانات
            current_pension = float(self.entry_vars["current_pension"].get().replace(',', ''))
            irg_rate = float(self.entry_vars["irg_rate"].get().replace(',', ''))
            taxable_gross = float(self.entry_vars["taxable_gross"].get().replace(',', ''))
            cnas = float(self.entry_vars["cnas"].get().replace(',', ''))
            increase_rate = float(self.entry_vars["increase_rate"].get())
            
            # التحقق من صحة البيانات
            if any(val < 0 for val in [current_pension, irg_rate, taxable_gross, cnas]):
                raise ValueError("لا يمكن أن تكون القيم سالبة")
            
            if increase_rate < 0 or increase_rate > 100:
                raise ValueError("نسبة الزيادة يجب أن تكون بين 0 و 100")
            
            # الحسابات
            calculations = self.perform_calculations(current_pension, irg_rate, taxable_gross, cnas, increase_rate)
            
            # تحديث النتائج
            self.update_results_display(calculations)
            
            # تحديث الإحصائيات
            self.update_statistics(calculations)
            
            # حفظ في التاريخ
            self.save_to_history(calculations)
            
            messagebox.showinfo("نجح الحساب", "تم حساب المعاش الجديد بنجاح!")
            
        except ValueError as e:
            messagebox.showerror("خطأ في البيانات", f"يرجى التأكد من صحة البيانات المدخلة:\n{str(e)}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def perform_calculations(self, current_pension, irg_rate, taxable_gross, cnas, increase_rate):
        """تنفيذ الحسابات"""
        # الحسابات الحالية
        current_gross = current_pension + irg_rate + cnas
        
        # الحسابات الجديدة
        increase_amount = current_gross * (increase_rate / 100)
        new_gross = current_gross + increase_amount
        new_cnas = new_gross * 0.02
        new_taxable = new_gross - new_cnas
        
        # حساب IRG الجديد
        irg_percentage = irg_rate / taxable_gross if taxable_gross > 0 else 0
        new_irg = new_taxable * irg_percentage
        
        # المعاش الصافي الجديد
        new_net_pension = new_gross - new_cnas - new_irg
        
        return {
            'current': {
                'net_pension': current_pension,
                'gross_total': current_gross,
                'cnas_amount': cnas,
                'irg_amount': irg_rate,
                'taxable_amount': taxable_gross
            },
            'new': {
                'net_pension': new_net_pension,
                'gross_total': new_gross,
                'cnas_amount': new_cnas,
                'irg_amount': new_irg,
                'taxable_amount': new_taxable
            },
            'increase': {
                'amount': increase_amount,
                'rate': increase_rate,
                'net_difference': new_net_pension - current_pension
            }
        }
    
    def update_results_display(self, calculations):
        """تحديث عرض النتائج"""
        current = calculations['current']
        new = calculations['new']
        
        items = ['net_pension', 'gross_total', 'cnas_amount', 'irg_amount', 'taxable_amount']
        
        for item in items:
            # القيمة الحالية
            self.result_labels[f"{item}_1"].config(text=f"{current[item]:,.2f}")
            # القيمة الجديدة
            self.result_labels[f"{item}_2"].config(text=f"{new[item]:,.2f}")
            # الفرق
            difference = new[item] - current[item]
            self.result_labels[f"{item}_3"].config(text=f"{difference:,.2f}")
            
            # تلوين الفرق
            if difference > 0:
                self.result_labels[f"{item}_3"].config(bg='#2ecc71', fg='white')
            elif difference < 0:
                self.result_labels[f"{item}_3"].config(bg='#e74c3c', fg='white')
            else:
                self.result_labels[f"{item}_3"].config(bg='#95a5a6', fg='white')
        
        # قيمة الزيادة
        self.result_labels["increase_value_1"].config(text="0.00")
        self.result_labels["increase_value_2"].config(text=f"{calculations['increase']['amount']:,.2f}")
        self.result_labels["increase_value_3"].config(text=f"{calculations['increase']['amount']:,.2f}")
        
        # نسبة الزيادة
        self.result_labels["increase_percent_1"].config(text="0%")
        self.result_labels["increase_percent_2"].config(text=f"{calculations['increase']['rate']}%")
        self.result_labels["increase_percent_3"].config(text=f"{calculations['increase']['rate']}%")
    
    def update_statistics(self, calculations):
        """تحديث الإحصائيات"""
        monthly_increase = calculations['increase']['net_difference']
        yearly_increase = monthly_increase * 12
        effective_rate = (monthly_increase / calculations['current']['net_pension']) * 100
        
        self.stats_labels["monthly_increase"].config(text=f"{monthly_increase:,.2f} دج")
        self.stats_labels["yearly_increase"].config(text=f"{yearly_increase:,.2f} دج")
        self.stats_labels["effective_rate"].config(text=f"{effective_rate:.2f}%")
    
    def save_to_history(self, calculations):
        """حفظ في تاريخ الحسابات"""
        entry = {
            'date': datetime.datetime.now().isoformat(),
            'calculations': calculations
        }
        self.history.append(entry)
        
        # الاحتفاظ بآخر 50 حساب فقط
        if len(self.history) > 50:
            self.history = self.history[-50:]
    
    def clear_data(self):
        """مسح جميع البيانات"""
        for var in self.entry_vars.values():
            var.set("")
        
        # مسح النتائج
        for label in self.result_labels.values():
            label.config(text="0.00", bg='white', fg='black')
        
        # مسح الإحصائيات
        for label in self.stats_labels.values():
            label.config(text="0.00 دج")
    
    def save_data(self):
        """حفظ البيانات في ملف"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                data = {var_name: var.get() for var_name, var in self.entry_vars.items()}
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح الحفظ", "تم حفظ البيانات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"فشل في حفظ البيانات: {str(e)}")
    
    def load_data(self):
        """تحميل البيانات من ملف"""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for var_name, value in data.items():
                    if var_name in self.entry_vars:
                        self.entry_vars[var_name].set(value)
                
                messagebox.showinfo("نجح التحميل", "تم تحميل البيانات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ في التحميل", f"فشل في تحميل البيانات: {str(e)}")
    
    def save_results(self):
        """حفظ النتائج"""
        messagebox.showinfo("قريباً", "ميزة حفظ النتائج ستكون متاحة قريباً!")
    
    def print_results(self):
        """طباعة النتائج"""
        messagebox.showinfo("قريباً", "ميزة طباعة النتائج ستكون متاحة قريباً!")
    
    def export_results(self):
        """تصدير النتائج"""
        messagebox.showinfo("قريباً", "ميزة تصدير النتائج ستكون متاحة قريباً!")
    
    def show_comparison(self):
        """عرض مقارنة النتائج"""
        messagebox.showinfo("قريباً", "ميزة مقارنة النتائج ستكون متاحة قريباً!")
    
    def show_history(self):
        """عرض تاريخ الحسابات"""
        if not self.history:
            messagebox.showinfo("تاريخ الحسابات", "لا يوجد حسابات محفوظة بعد.")
            return
        
        history_window = tk.Toplevel(self.root)
        history_window.title("تاريخ الحسابات")
        history_window.geometry("600x400")
        
        # قائمة التاريخ
        listbox = tk.Listbox(history_window, font=self.arabic_font)
        listbox.pack(fill='both', expand=True, padx=10, pady=10)
        
        for i, entry in enumerate(self.history):
            date_str = datetime.datetime.fromisoformat(entry['date']).strftime('%Y-%m-%d %H:%M')
            increase = entry['calculations']['increase']['net_difference']
            listbox.insert(tk.END, f"{i+1}. {date_str} - زيادة: {increase:,.2f} دج")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
حاسبة معاش المتقاعدين المحسنة
الإصدار 2.0

تطبيق متقدم لحساب الزيادة في معاشات المتقاعدين
مع واجهة مستخدم محسنة ومميزات إضافية

المطور: نظام حساب المعاشات
التاريخ: 2024
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
دليل الاستخدام:

1. أدخل البيانات المطلوبة في الحقول
2. اضغط على "حساب المعاش الجديد"
3. ستظهر النتائج في الجدول التفصيلي
4. يمكنك حفظ وتحميل البيانات
5. راجع الإحصائيات السريعة أسفل الشاشة

للمساعدة الإضافية، راجع ملف README.md
        """
        messagebox.showinfo("دليل الاستخدام", help_text)

def main():
    root = tk.Tk()
    app = EnhancedPensionCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
