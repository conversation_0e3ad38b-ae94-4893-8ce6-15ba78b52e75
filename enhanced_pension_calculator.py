#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق حساب معاش المتقاعدين المحسن
Enhanced Pension Calculator Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import datetime
import os
import csv
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.units import inch
    from reportlab.lib.enums import TA_CENTER, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("تحذير: مكتبة reportlab غير مثبتة. ميزة PDF غير متاحة.")
    print("لتثبيتها: pip install reportlab")

class EnhancedPensionCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("حاسبة معاش المتقاعدين المحسنة - Enhanced Pension Calculator")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2c3e50')

        # تكوين الخطوط المحسنة مع زيادة الحجم
        self.arabic_font = ('Arial Unicode MS', 14)
        self.title_font = ('Arial Unicode MS', 22, 'bold')
        self.result_font = ('Arial Unicode MS', 15, 'bold')
        self.small_font = ('Arial Unicode MS', 12)

        # متغيرات الحفظ والاستعادة
        self.history = []

        # متغيرات لتتبع حالة النافذة
        self.window_state = 'normal'
        self.last_geometry = "1200x800"
        self.is_maximized = False

        # ربط أحداث تغيير حجم النافذة
        self.root.bind('<Configure>', self.on_window_resize)

        self.create_widgets()
        self.create_menu()

        # تحديث دوري لرصد تغيير حالة النافذة
        self.check_window_state()

    def on_window_resize(self, event):
        """تعامل مع تغيير حجم النافذة"""
        if event.widget == self.root:
            # رصد تغيير حالة النافذة (تكبير/تصغير)
            current_state = self.root.state()

            # إذا تم تكبير النافذة
            if current_state == 'zoomed' and not self.is_maximized:
                self.is_maximized = True
                self.expand_interface_to_window()
            # إذا تم إلغاء تكبير النافذة
            elif current_state == 'normal' and self.is_maximized:
                self.is_maximized = False
                self.restore_interface_size()

    def check_window_state(self):
        """فحص حالة النافذة بشكل دوري"""
        try:
            current_state = self.root.state()

            # إذا تغيرت حالة النافذة
            if current_state != self.window_state:
                self.window_state = current_state

                if current_state == 'zoomed' and not self.is_maximized:
                    self.is_maximized = True
                    self.expand_interface_to_window()
                elif current_state == 'normal' and self.is_maximized:
                    self.is_maximized = False
                    self.restore_interface_size()

            # تكرار الفحص بعد 100 ملي ثانية
            self.root.after(100, self.check_window_state)
        except:
            # في حالة حدوث خطأ، أعد المحاولة بعد ثانية
            self.root.after(1000, self.check_window_state)

    def expand_interface_to_window(self):
        """توسيع الواجهة لتملأ النافذة المكبرة"""
        try:
            # انتظار قصير للتأكد من اكتمال تكبير النافذة
            self.root.after(50, self._do_expand_interface)
        except Exception as e:
            print(f"Error expanding interface: {e}")

    def _do_expand_interface(self):
        """تنفيذ توسيع الواجهة الفعلي"""
        try:
            # الحصول على حجم النافذة الحالي بعد التكبير
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()

            print(f"Window size after maximizing: {width}x{height}")

            # حساب حجم الخط المناسب بناءً على حجم النافذة الفعلي
            if width > 1800:
                font_size = 20
                title_size = 32
            elif width > 1600:
                font_size = 18
                title_size = 28
            elif width > 1400:
                font_size = 16
                title_size = 24
            elif width > 1200:
                font_size = 15
                title_size = 22
            else:
                font_size = 14
                title_size = 20

            print(f"Setting font size to: {font_size}pt, title: {title_size}pt")

            # تحديث الخطوط
            self.arabic_font = ('Arial Unicode MS', font_size)
            self.title_font = ('Arial Unicode MS', title_size, 'bold')
            self.result_font = ('Arial Unicode MS', font_size + 1, 'bold')
            self.small_font = ('Arial Unicode MS', font_size - 2)

            # تحديث حجم النافذة في الإعدادات إذا لم تكن مكبرة بالكامل
            if self.root.state() == 'zoomed':
                # في حالة التكبير، استخدم كامل الشاشة
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                print(f"Screen size: {screen_width}x{screen_height}")

                # تأكد من أن النافذة تأخذ كامل الشاشة
                self.root.geometry(f"{screen_width}x{screen_height}+0+0")

            # إعادة إنشاء الواجهة
            self.refresh_interface()

            # تحديث إضافي للتأكد من ملء النافذة
            self.root.after(100, self._ensure_full_window)

        except Exception as e:
            print(f"Error in _do_expand_interface: {e}")

    def _ensure_full_window(self):
        """التأكد من أن الواجهة تملأ النافذة بالكامل"""
        try:
            if self.root.state() == 'zoomed':
                # فرض تحديث حجم النافذة
                self.root.update_idletasks()

                # الحصول على حجم الشاشة
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()

                # تعيين النافذة لتأخذ كامل الشاشة
                self.root.geometry(f"{screen_width}x{screen_height}+0+0")
                self.root.state('zoomed')  # التأكد من حالة التكبير

                print(f"Ensured full window: {screen_width}x{screen_height}")
        except Exception as e:
            print(f"Error in _ensure_full_window: {e}")

    def _update_scroll_region(self, canvas):
        """تحديث منطقة التمرير"""
        try:
            canvas.configure(scrollregion=canvas.bbox("all"))
        except Exception as e:
            print(f"Error updating scroll region: {e}")

    def _center_interface(self, canvas, frame):
        """محاذاة الواجهة في وسط النافذة"""
        try:
            # انتظار قصير للتأكد من اكتمال تحديث النافذة
            canvas.update_idletasks()

            # الحصول على أبعاد النافذة والإطار
            canvas_width = canvas.winfo_width()
            canvas_height = canvas.winfo_height()
            frame.update_idletasks()
            frame_width = frame.winfo_reqwidth()
            frame_height = frame.winfo_reqheight()

            # حساب موضع الوسط
            center_x = canvas_width // 2
            center_y = max(canvas_height // 2, frame_height // 2)

            # تحديث موضع الإطار في الوسط
            canvas.coords(self.canvas_window, center_x, center_y)

            # تحديث منطقة التمرير
            canvas.configure(scrollregion=canvas.bbox("all"))

            print(f"Interface centered at: {center_x}, {center_y}")
            print(f"Canvas size: {canvas_width}x{canvas_height}")
            print(f"Frame size: {frame_width}x{frame_height}")

        except Exception as e:
            print(f"Error centering interface: {e}")

    def _update_scroll_and_expand(self, canvas, frame):
        """تحديث منطقة التمرير وتوسيع الواجهة"""
        try:
            # تحديث منطقة التمرير
            canvas.configure(scrollregion=canvas.bbox("all"))

            # توسيع الواجهة لتملأ عرض النافذة
            self._expand_interface_to_fill(canvas, frame)
        except Exception as e:
            print(f"Error updating scroll and expand: {e}")

    def _expand_interface_to_fill(self, canvas, frame):
        """توسيع الواجهة لتملأ عرض النافذة بالكامل"""
        try:
            # انتظار قصير للتأكد من اكتمال تحديث النافذة
            canvas.update_idletasks()

            # الحصول على أبعاد النافذة
            canvas_width = canvas.winfo_width()
            canvas_height = canvas.winfo_height()

            # تعيين عرض الإطار ليملأ عرض النافذة
            if canvas_width > 1:
                # تعيين عرض الإطار ليساوي عرض النافذة
                canvas.itemconfig(self.canvas_window, width=canvas_width)

                # تحديث موضع الإطار في الزاوية اليسرى العلوية
                canvas.coords(self.canvas_window, 0, 0)

                print(f"Interface expanded to fill window: {canvas_width}x{canvas_height}")

        except Exception as e:
            print(f"Error expanding interface to fill: {e}")

    def restore_interface_size(self):
        """إعادة الواجهة إلى الحجم العادي"""
        try:
            # إعادة تعيين الخطوط إلى الحجم الافتراضي
            self.arabic_font = ('Arial Unicode MS', 14)
            self.title_font = ('Arial Unicode MS', 22, 'bold')
            self.result_font = ('Arial Unicode MS', 15, 'bold')
            self.small_font = ('Arial Unicode MS', 12)

            # إعادة إنشاء الواجهة
            self.refresh_interface()
        except Exception as e:
            print(f"Error restoring interface: {e}")

    def change_font_size(self, size):
        """تغيير حجم الخط والنافذة"""
        if size == 'small':
            base_size = 12
            title_size = 18
            window_width = 1000
            window_height = 700
        elif size == 'medium':
            base_size = 14
            title_size = 22
            window_width = 1200
            window_height = 800
        elif size == 'large':
            base_size = 16
            title_size = 26
            window_width = 1400
            window_height = 900
        elif size == 'xlarge':
            base_size = 18
            title_size = 30
            window_width = 1600
            window_height = 1000
        else:
            base_size = 14
            title_size = 22
            window_width = 1200
            window_height = 800

        # تحديث الخطوط
        self.arabic_font = ('Arial Unicode MS', base_size)
        self.title_font = ('Arial Unicode MS', title_size, 'bold')
        self.result_font = ('Arial Unicode MS', base_size + 1, 'bold')
        self.small_font = ('Arial Unicode MS', base_size - 2)

        # تغيير حجم النافذة
        self.root.geometry(f"{window_width}x{window_height}")

        # إعادة إنشاء الواجهة
        self.refresh_interface()

    def refresh_interface(self):
        """تحديث الواجهة بعد تغيير الخط"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            if widget.winfo_class() != 'Menu':
                widget.destroy()

        # إعادة إنشاء الواجهة
        self.create_widgets()

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)

        if not current_state:
            # في وضع ملء الشاشة
            self.root.bind('<Escape>', lambda e: self.root.attributes('-fullscreen', False))
        else:
            # خروج من وضع ملء الشاشة
            self.root.unbind('<Escape>')

    def reset_window(self):
        """إعادة تعيين النافذة"""
        self.root.attributes('-fullscreen', False)
        self.change_font_size('medium')  # إعادة تعيين إلى الحجم المتوسط
        self.root.state('normal')

    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="حفظ البيانات", command=self.save_data)
        file_menu.add_command(label="تحميل البيانات", command=self.load_data)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير النتائج", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)

        # قائمة العرض
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="عرض", menu=view_menu)

        # قائمة فرعية لحجم الخط والنافذة
        font_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="حجم الخط والنافذة", menu=font_menu)
        font_menu.add_command(label="صغير (1000x700)", command=lambda: self.change_font_size('small'))
        font_menu.add_command(label="متوسط (1200x800)", command=lambda: self.change_font_size('medium'))
        font_menu.add_command(label="كبير (1400x900)", command=lambda: self.change_font_size('large'))
        font_menu.add_command(label="كبير جداً (1600x1000)", command=lambda: self.change_font_size('xlarge'))

        view_menu.add_separator()
        view_menu.add_command(label="ملء الشاشة", command=self.toggle_fullscreen)
        view_menu.add_command(label="إعادة تعيين النافذة", command=self.reset_window)

        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="مقارنة النتائج", command=self.show_comparison)
        tools_menu.add_command(label="تاريخ الحسابات", command=self.show_history)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)

    def create_widgets(self):
        # إطار رئيسي مع شريط تمرير يملأ النافذة
        main_canvas = tk.Canvas(self.root, bg='#2c3e50')
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=main_canvas.yview)

        # إطار قابل للتمرير يملأ عرض النافذة
        scrollable_frame = tk.Frame(main_canvas, bg='#2c3e50')

        # ربط تحديث منطقة التمرير وتوسيع الواجهة
        scrollable_frame.bind(
            "<Configure>",
            lambda e: self._update_scroll_and_expand(main_canvas, scrollable_frame)
        )

        # ربط تحديث توسيع الواجهة عند تغيير حجم النافذة
        main_canvas.bind("<Configure>", lambda e: self._expand_interface_to_fill(main_canvas, scrollable_frame))

        # إنشاء نافذة تملأ عرض النافذة
        self.canvas_window = main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # العنوان الرئيسي مع التاريخ
        title_frame = tk.Frame(scrollable_frame, bg='#34495e', relief='raised', bd=3)
        title_frame.pack(fill='x', padx=10, pady=10)

        title_label = tk.Label(title_frame, text="حاسبة الزيادة في معاشات المتقاعدين",
                              font=self.title_font, bg='#34495e', fg='#ecf0f1')
        title_label.pack(pady=5)

        date_label = tk.Label(title_frame, text=f"التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}",
                             font=self.small_font, bg='#34495e', fg='#bdc3c7')
        date_label.pack(pady=2)

        # إطار الإدخال المحسن
        input_frame = tk.LabelFrame(scrollable_frame, text="بيانات الإدخال", font=self.arabic_font,
                                   bg='#ecf0f1', fg='#2c3e50', relief='groove', bd=3)
        input_frame.pack(fill='x', padx=10, pady=5)

        # إنشاء الحقول مع التحقق من صحة البيانات
        self.create_input_fields(input_frame)

        # أزرار التحكم المحسنة
        self.create_control_buttons(scrollable_frame)

        # إطار النتائج المحسن
        self.create_enhanced_results_frame(scrollable_frame)

        # إطار الإحصائيات
        self.create_statistics_frame(scrollable_frame)

    def create_input_fields(self, parent):
        """إنشاء حقول الإدخال مع التحقق"""
        fields = [
            ("المعاش الصافي الحالي (دج):", "current_pension", "40608.00"),
            ("نسبة IRG المحسوبة (دج):", "irg_rate", "4472.00"),
            ("الخام الخاضع للضريبة (دج):", "taxable_gross", "43080.00"),
            ("CNAS 2% (دج):", "cnas", "920.00"),
            ("نسبة الزيادة (%):", "increase_rate", "15")
        ]

        self.entry_vars = {}

        for i, (label_text, var_name, default_value) in enumerate(fields):
            # إطار لكل حقل
            field_frame = tk.Frame(parent, bg='#ecf0f1')
            field_frame.grid(row=i//2, column=(i%2)*2, columnspan=2, sticky='ew', padx=5, pady=3)

            # التسمية
            label = tk.Label(field_frame, text=label_text, font=self.arabic_font,
                           bg='#ecf0f1', fg='#2c3e50', width=25, anchor='e')
            label.pack(side='right', padx=5)

            # حقل الإدخال
            var = tk.StringVar(value=default_value)
            entry = tk.Entry(field_frame, textvariable=var, font=self.arabic_font,
                           width=15, justify='center', relief='sunken', bd=2)
            entry.pack(side='right', padx=5)

            # ربط التحقق من البيانات
            entry.bind('<KeyRelease>', lambda e, v=var: self.validate_input(v))

            self.entry_vars[var_name] = var

        # تكوين الشبكة
        for i in range(3):
            parent.grid_columnconfigure(i, weight=1)

    def validate_input(self, var):
        """التحقق من صحة البيانات المدخلة"""
        try:
            value = var.get().replace(',', '')
            if value and float(value) < 0:
                var.set("0")
        except ValueError:
            pass

    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        button_frame = tk.Frame(parent, bg='#2c3e50')
        button_frame.pack(fill='x', padx=10, pady=10)

        buttons = [
            ("حساب المعاش الجديد", self.calculate_pension, '#27ae60'),
            ("مسح البيانات", self.clear_data, '#e74c3c'),
            ("حفظ النتائج", self.save_results, '#3498db'),
            ("طباعة النتائج", self.print_results, '#9b59b6')
        ]

        for text, command, color in buttons:
            btn = tk.Button(button_frame, text=text, font=self.arabic_font,
                          bg=color, fg='white', command=command,
                          relief='raised', bd=3, padx=20, pady=5)
            btn.pack(side='right', padx=5)

    def create_enhanced_results_frame(self, parent):
        """إنشاء إطار النتائج المحسن"""
        self.result_frame = tk.LabelFrame(parent, text="النتائج التفصيلية", font=self.arabic_font,
                                         bg='#ecf0f1', fg='#2c3e50', relief='groove', bd=3)
        self.result_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إنشاء جدول النتائج المحسن
        self.create_enhanced_results_table()

    def create_enhanced_results_table(self):
        """إنشاء جدول النتائج المحسن"""
        # إطار للجدول مع شريط تمرير
        table_container = tk.Frame(self.result_frame, bg='#ecf0f1')
        table_container.pack(fill='both', expand=True, padx=10, pady=10)

        # عناوين الأعمدة
        headers = ["البيان", "القيمة الحالية", "القيمة الجديدة", "الفرق"]
        colors = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12']

        for i, (header, color) in enumerate(zip(headers, colors)):
            label = tk.Label(table_container, text=header, font=self.result_font,
                           bg=color, fg='white', relief='raised', bd=2, pady=5)
            label.grid(row=0, column=i, sticky='ew', padx=1, pady=1)

        # صفوف البيانات
        self.result_labels = {}
        result_items = [
            ("المعاش الصافي", "net_pension"),
            ("الخام الإجمالي", "gross_total"),
            ("CNAS (2%)", "cnas_amount"),
            ("IRG المحسوب", "irg_amount"),
            ("الخام الخاضع للضريبة", "taxable_amount"),
            ("قيمة الزيادة", "increase_value"),
            ("نسبة الزيادة", "increase_percent")
        ]

        for i, (label_text, key) in enumerate(result_items, 1):
            # عمود البيان
            tk.Label(table_container, text=label_text, font=self.arabic_font,
                    bg='#bdc3c7', relief='raised', bd=1, pady=3).grid(row=i, column=0,
                    sticky='ew', padx=1, pady=1)

            # الأعمدة الأخرى
            for j in range(1, 4):
                value_label = tk.Label(table_container, text="0.00", font=self.arabic_font,
                                     bg='white', relief='sunken', bd=1, pady=3)
                value_label.grid(row=i, column=j, sticky='ew', padx=1, pady=1)
                self.result_labels[f"{key}_{j}"] = value_label

        # تكوين الأعمدة
        for i in range(4):
            table_container.grid_columnconfigure(i, weight=1)

    def create_statistics_frame(self, parent):
        """إنشاء إطار الإحصائيات"""
        stats_frame = tk.LabelFrame(parent, text="إحصائيات سريعة", font=self.arabic_font,
                                   bg='#ecf0f1', fg='#2c3e50', relief='groove', bd=3)
        stats_frame.pack(fill='x', padx=10, pady=5)

        self.stats_labels = {}
        stats_items = [
            ("إجمالي الزيادة الشهرية:", "monthly_increase"),
            ("إجمالي الزيادة السنوية:", "yearly_increase"),
            ("نسبة الزيادة الفعلية:", "effective_rate")
        ]

        for i, (text, key) in enumerate(stats_items):
            frame = tk.Frame(stats_frame, bg='#ecf0f1')
            frame.pack(side='left', expand=True, fill='x', padx=10, pady=5)

            tk.Label(frame, text=text, font=self.arabic_font,
                    bg='#ecf0f1', fg='#2c3e50').pack(side='top')

            value_label = tk.Label(frame, text="0.00 دج", font=self.result_font,
                                 bg='#34495e', fg='white', relief='raised', bd=2, pady=5)
            value_label.pack(side='top', fill='x')
            self.stats_labels[key] = value_label

    def calculate_pension(self):
        """حساب المعاش الجديد مع التحسينات"""
        try:
            # قراءة البيانات
            current_pension = float(self.entry_vars["current_pension"].get().replace(',', ''))
            irg_rate = float(self.entry_vars["irg_rate"].get().replace(',', ''))
            taxable_gross = float(self.entry_vars["taxable_gross"].get().replace(',', ''))
            cnas = float(self.entry_vars["cnas"].get().replace(',', ''))
            increase_rate = float(self.entry_vars["increase_rate"].get())

            # التحقق من صحة البيانات
            if any(val < 0 for val in [current_pension, irg_rate, taxable_gross, cnas]):
                raise ValueError("لا يمكن أن تكون القيم سالبة")

            if increase_rate < 0 or increase_rate > 100:
                raise ValueError("نسبة الزيادة يجب أن تكون بين 0 و 100")

            # الحسابات
            calculations = self.perform_calculations(current_pension, irg_rate, taxable_gross, cnas, increase_rate)

            # تحديث النتائج
            self.update_results_display(calculations)

            # تحديث الإحصائيات
            self.update_statistics(calculations)

            # حفظ في التاريخ
            self.save_to_history(calculations)

            messagebox.showinfo("نجح الحساب", "تم حساب المعاش الجديد بنجاح!")

        except ValueError as e:
            messagebox.showerror("خطأ في البيانات", f"يرجى التأكد من صحة البيانات المدخلة:\n{str(e)}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def perform_calculations(self, current_pension, irg_rate, taxable_gross, cnas, increase_rate):
        """تنفيذ الحسابات"""
        # الحسابات الحالية
        current_gross = current_pension + irg_rate + cnas

        # الحسابات الجديدة
        increase_amount = current_gross * (increase_rate / 100)
        new_gross = current_gross + increase_amount
        new_cnas = new_gross * 0.02
        new_taxable = new_gross - new_cnas

        # حساب IRG الجديد
        irg_percentage = irg_rate / taxable_gross if taxable_gross > 0 else 0
        new_irg = new_taxable * irg_percentage

        # المعاش الصافي الجديد
        new_net_pension = new_gross - new_cnas - new_irg

        return {
            'current': {
                'net_pension': current_pension,
                'gross_total': current_gross,
                'cnas_amount': cnas,
                'irg_amount': irg_rate,
                'taxable_amount': taxable_gross
            },
            'new': {
                'net_pension': new_net_pension,
                'gross_total': new_gross,
                'cnas_amount': new_cnas,
                'irg_amount': new_irg,
                'taxable_amount': new_taxable
            },
            'increase': {
                'amount': increase_amount,
                'rate': increase_rate,
                'net_difference': new_net_pension - current_pension
            }
        }

    def update_results_display(self, calculations):
        """تحديث عرض النتائج"""
        current = calculations['current']
        new = calculations['new']

        items = ['net_pension', 'gross_total', 'cnas_amount', 'irg_amount', 'taxable_amount']

        for item in items:
            # القيمة الحالية
            self.result_labels[f"{item}_1"].config(text=f"{current[item]:,.2f}")
            # القيمة الجديدة
            self.result_labels[f"{item}_2"].config(text=f"{new[item]:,.2f}")
            # الفرق
            difference = new[item] - current[item]
            self.result_labels[f"{item}_3"].config(text=f"{difference:,.2f}")

            # تلوين الفرق
            if difference > 0:
                self.result_labels[f"{item}_3"].config(bg='#2ecc71', fg='white')
            elif difference < 0:
                self.result_labels[f"{item}_3"].config(bg='#e74c3c', fg='white')
            else:
                self.result_labels[f"{item}_3"].config(bg='#95a5a6', fg='white')

        # قيمة الزيادة
        self.result_labels["increase_value_1"].config(text="0.00")
        self.result_labels["increase_value_2"].config(text=f"{calculations['increase']['amount']:,.2f}")
        self.result_labels["increase_value_3"].config(text=f"{calculations['increase']['amount']:,.2f}")

        # نسبة الزيادة
        self.result_labels["increase_percent_1"].config(text="0%")
        self.result_labels["increase_percent_2"].config(text=f"{calculations['increase']['rate']}%")
        self.result_labels["increase_percent_3"].config(text=f"{calculations['increase']['rate']}%")

    def update_statistics(self, calculations):
        """تحديث الإحصائيات"""
        monthly_increase = calculations['increase']['net_difference']
        yearly_increase = monthly_increase * 12
        effective_rate = (monthly_increase / calculations['current']['net_pension']) * 100

        self.stats_labels["monthly_increase"].config(text=f"{monthly_increase:,.2f} دج")
        self.stats_labels["yearly_increase"].config(text=f"{yearly_increase:,.2f} دج")
        self.stats_labels["effective_rate"].config(text=f"{effective_rate:.2f}%")

    def save_to_history(self, calculations):
        """حفظ في تاريخ الحسابات"""
        entry = {
            'date': datetime.datetime.now().isoformat(),
            'calculations': calculations
        }
        self.history.append(entry)

        # الاحتفاظ بآخر 50 حساب فقط
        if len(self.history) > 50:
            self.history = self.history[-50:]

    def clear_data(self):
        """مسح جميع البيانات"""
        for var in self.entry_vars.values():
            var.set("")

        # مسح النتائج
        for label in self.result_labels.values():
            label.config(text="0.00", bg='white', fg='black')

        # مسح الإحصائيات
        for label in self.stats_labels.values():
            label.config(text="0.00 دج")

    def save_data(self):
        """حفظ البيانات في ملف"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                data = {var_name: var.get() for var_name, var in self.entry_vars.items()}
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح الحفظ", "تم حفظ البيانات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"فشل في حفظ البيانات: {str(e)}")

    def load_data(self):
        """تحميل البيانات من ملف"""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                for var_name, value in data.items():
                    if var_name in self.entry_vars:
                        self.entry_vars[var_name].set(value)

                messagebox.showinfo("نجح التحميل", "تم تحميل البيانات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ في التحميل", f"فشل في تحميل البيانات: {str(e)}")

    def save_results(self):
        """حفظ النتائج في ملف JSON أو CSV"""
        if not self.history:
            messagebox.showwarning("تحذير", "لا توجد نتائج لحفظها. يرجى حساب المعاش أولاً.")
            return

        try:
            # اختيار نوع الملف
            file_types = [
                ("JSON files", "*.json"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]

            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=file_types,
                title="حفظ النتائج"
            )

            if filename:
                if filename.lower().endswith('.csv'):
                    self.save_results_csv(filename)
                else:
                    self.save_results_json(filename)

                messagebox.showinfo("نجح الحفظ", f"تم حفظ النتائج بنجاح في:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"فشل في حفظ النتائج: {str(e)}")

    def save_results_json(self, filename):
        """حفظ النتائج في ملف JSON"""
        # إعداد بيانات الحفظ
        latest_result = self.history[-1] if self.history else None

        save_data = {
            'timestamp': datetime.datetime.now().isoformat(),
            'application': 'حاسبة معاش المتقاعدين المحسنة',
            'latest_calculation': latest_result,
            'all_calculations': self.history,
            'input_data': {var_name: var.get() for var_name, var in self.entry_vars.items()}
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

    def save_results_csv(self, filename):
        """حفظ النتائج في ملف CSV"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)

            # كتابة العناوين
            writer.writerow([
                'التاريخ', 'المعاش الحالي', 'المعاش الجديد',
                'قيمة الزيادة', 'نسبة الزيادة', 'الخام الحالي',
                'الخام الجديد', 'CNAS الحالي', 'CNAS الجديد',
                'IRG الحالي', 'IRG الجديد'
            ])

            # كتابة البيانات
            for entry in self.history:
                calc = entry['calculations']
                date_str = datetime.datetime.fromisoformat(entry['date']).strftime('%Y-%m-%d %H:%M')

                writer.writerow([
                    date_str,
                    f"{calc['current']['net_pension']:,.2f}",
                    f"{calc['new']['net_pension']:,.2f}",
                    f"{calc['increase']['amount']:,.2f}",
                    f"{calc['increase']['rate']:.2f}%",
                    f"{calc['current']['gross_total']:,.2f}",
                    f"{calc['new']['gross_total']:,.2f}",
                    f"{calc['current']['cnas_amount']:,.2f}",
                    f"{calc['new']['cnas_amount']:,.2f}",
                    f"{calc['current']['irg_amount']:,.2f}",
                    f"{calc['new']['irg_amount']:,.2f}"
                ])

    def print_results(self):
        """طباعة النتائج في ملف PDF"""
        if not self.history:
            messagebox.showwarning("تحذير", "لا توجد نتائج لطباعتها. يرجى حساب المعاش أولاً.")
            return

        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة reportlab غير متاحة.\nلتثبيتها: pip install reportlab")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ تقرير PDF"
            )

            if filename:
                self.create_pdf_report(filename)
                messagebox.showinfo("نجح الطباعة", f"تم إنشاء تقرير PDF بنجاح:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ في الطباعة", f"فشل في إنشاء تقرير PDF: {str(e)}")

    def create_pdf_report(self, filename):
        """إنشاء تقرير PDF مفصل مع دعم العربية"""
        from reportlab.lib.pagesizes import A4
        from reportlab.lib import colors
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.units import inch
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT

        # تسجيل خط عربي (إذا كان متاحاً)
        arabic_font = self._register_arabic_font()

        # إعداد المستند
        doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

        # قائمة العناصر
        story = []

        # العنوان الرئيسي
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=getSampleStyleSheet()['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue,
            fontName=arabic_font
        )

        story.append(Paragraph("Pension Calculation Report - تقرير حساب المعاش", title_style))
        story.append(Spacer(1, 12))

        # معلومات عامة
        info_style = ParagraphStyle(
            'InfoStyle',
            parent=getSampleStyleSheet()['Normal'],
            fontSize=12,
            alignment=TA_RIGHT,
            fontName=arabic_font
        )

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        story.append(Paragraph(f"Report Date - تاريخ التقرير: {current_time}", info_style))
        story.append(Paragraph(f"Number of Calculations - عدد الحسابات: {len(self.history)}", info_style))
        story.append(Spacer(1, 20))

        # الحساب الأخير
        if self.history:
            latest = self.history[-1]['calculations']

            # عنوان الحساب الأخير
            subtitle_style = ParagraphStyle(
                'SubTitle',
                parent=getSampleStyleSheet()['Heading2'],
                fontSize=14,
                spaceAfter=15,
                alignment=TA_CENTER,
                textColor=colors.darkgreen,
                fontName=arabic_font
            )

            story.append(Paragraph("Latest Calculation - الحساب الأخير", subtitle_style))

            # جدول النتائج (بالإنجليزية لتجنب مشاكل الخطوط)
            data = [
                ['Difference', 'New Value', 'Current Value', 'Item'],
                [
                    f"{latest['new']['net_pension'] - latest['current']['net_pension']:,.2f}",
                    f"{latest['new']['net_pension']:,.2f}",
                    f"{latest['current']['net_pension']:,.2f}",
                    'Net Pension'
                ],
                [
                    f"{latest['new']['gross_total'] - latest['current']['gross_total']:,.2f}",
                    f"{latest['new']['gross_total']:,.2f}",
                    f"{latest['current']['gross_total']:,.2f}",
                    'Gross Total'
                ],
                [
                    f"{latest['new']['cnas_amount'] - latest['current']['cnas_amount']:,.2f}",
                    f"{latest['new']['cnas_amount']:,.2f}",
                    f"{latest['current']['cnas_amount']:,.2f}",
                    'CNAS (2%)'
                ],
                [
                    f"{latest['new']['irg_amount'] - latest['current']['irg_amount']:,.2f}",
                    f"{latest['new']['irg_amount']:,.2f}",
                    f"{latest['current']['irg_amount']:,.2f}",
                    'IRG Tax'
                ]
            ]

            table = Table(data, colWidths=[1.5*inch, 1.5*inch, 1.5*inch, 2*inch])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)
            story.append(Spacer(1, 20))

            # معلومات إضافية (بالإنجليزية)
            story.append(Paragraph(f"Increase Amount: {latest['increase']['amount']:,.2f} DZD", info_style))
            story.append(Paragraph(f"Increase Rate: {latest['increase']['rate']:.2f}%", info_style))
            story.append(Paragraph(f"Monthly Increase: {latest['increase']['net_difference']:,.2f} DZD", info_style))
            story.append(Paragraph(f"Annual Increase: {latest['increase']['net_difference'] * 12:,.2f} DZD", info_style))

            # إضافة جدول ملخص بالعربية
            story.append(Spacer(1, 20))
            story.append(Paragraph("Summary - ملخص", subtitle_style))

            summary_data = [
                ['Value - القيمة', 'Description - الوصف'],
                [f"{latest['current']['net_pension']:,.2f} DZD", "Current Net Pension"],
                [f"{latest['new']['net_pension']:,.2f} DZD", "New Net Pension"],
                [f"{latest['increase']['amount']:,.2f} DZD", "Total Increase"],
                [f"{latest['increase']['rate']:.2f}%", "Increase Percentage"],
                [f"{latest['increase']['net_difference']:,.2f} DZD", "Monthly Net Increase"]
            ]

            summary_table = Table(summary_data, colWidths=[3*inch, 3*inch])
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(summary_table)

        # بناء المستند
        doc.build(story)

    def _register_arabic_font(self):
        """تسجيل خط عربي إذا كان متاحاً"""
        try:
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # محاولة تسجيل خط عربي
            arabic_fonts = [
                'arial.ttf',
                'tahoma.ttf',
                'calibri.ttf',
                'DejaVuSans.ttf'
            ]

            for font_file in arabic_fonts:
                try:
                    pdfmetrics.registerFont(TTFont('Arabic', font_file))
                    print(f"Arabic font registered: {font_file}")
                    return 'Arabic'
                except:
                    continue

            # إذا لم يتم العثور على خط عربي، استخدم الخط الافتراضي
            print("No Arabic font found, using default Helvetica")
            return 'Helvetica'

        except Exception as e:
            print(f"Error registering Arabic font: {e}")
            return 'Helvetica'

    def export_results(self):
        """تصدير النتائج بأشكال مختلفة"""
        if not self.history:
            messagebox.showwarning("تحذير", "لا توجد نتائج لتصديرها. يرجى حساب المعاش أولاً.")
            return

        # نافذة اختيار نوع التصدير
        export_window = tk.Toplevel(self.root)
        export_window.title("تصدير النتائج")
        export_window.geometry("400x300")
        export_window.configure(bg='#2c3e50')
        export_window.transient(self.root)
        export_window.grab_set()

        # عنوان
        title_label = tk.Label(export_window, text="اختر نوع التصدير",
                              font=self.title_font, bg='#2c3e50', fg='white')
        title_label.pack(pady=20)

        # أزرار التصدير
        button_frame = tk.Frame(export_window, bg='#2c3e50')
        button_frame.pack(expand=True, fill='both', padx=20, pady=20)

        buttons = [
            ("حفظ JSON", lambda: self.export_and_close(export_window, 'json'), '#3498db'),
            ("حفظ CSV", lambda: self.export_and_close(export_window, 'csv'), '#2ecc71'),
            ("طباعة PDF", lambda: self.export_and_close(export_window, 'pdf'), '#e74c3c'),
            ("إلغاء", export_window.destroy, '#95a5a6')
        ]

        for text, command, color in buttons:
            btn = tk.Button(button_frame, text=text, font=self.arabic_font,
                          bg=color, fg='white', command=command,
                          relief='raised', bd=3, padx=20, pady=10)
            btn.pack(pady=5, fill='x')

    def export_and_close(self, window, export_type):
        """تصدير وإغلاق النافذة"""
        window.destroy()

        if export_type == 'json':
            self.save_results()
        elif export_type == 'csv':
            self.save_results()
        elif export_type == 'pdf':
            self.print_results()

    def show_comparison(self):
        """عرض مقارنة النتائج"""
        messagebox.showinfo("قريباً", "ميزة مقارنة النتائج ستكون متاحة قريباً!")

    def show_history(self):
        """عرض تاريخ الحسابات"""
        if not self.history:
            messagebox.showinfo("تاريخ الحسابات", "لا يوجد حسابات محفوظة بعد.")
            return

        history_window = tk.Toplevel(self.root)
        history_window.title("تاريخ الحسابات")
        history_window.geometry("600x400")

        # قائمة التاريخ
        listbox = tk.Listbox(history_window, font=self.arabic_font)
        listbox.pack(fill='both', expand=True, padx=10, pady=10)

        for i, entry in enumerate(self.history):
            date_str = datetime.datetime.fromisoformat(entry['date']).strftime('%Y-%m-%d %H:%M')
            increase = entry['calculations']['increase']['net_difference']
            listbox.insert(tk.END, f"{i+1}. {date_str} - زيادة: {increase:,.2f} دج")

    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
حاسبة معاش المتقاعدين المحسنة
الإصدار 2.0

تطبيق متقدم لحساب الزيادة في معاشات المتقاعدين
مع واجهة مستخدم محسنة ومميزات إضافية

المطور: نظام حساب المعاشات
التاريخ: 2024
        """
        messagebox.showinfo("حول البرنامج", about_text)

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
دليل الاستخدام:

1. أدخل البيانات المطلوبة في الحقول
2. اضغط على "حساب المعاش الجديد"
3. ستظهر النتائج في الجدول التفصيلي
4. يمكنك حفظ وتحميل البيانات
5. راجع الإحصائيات السريعة أسفل الشاشة

للمساعدة الإضافية، راجع ملف README.md
        """
        messagebox.showinfo("دليل الاستخدام", help_text)

def main():
    root = tk.Tk()
    app = EnhancedPensionCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
