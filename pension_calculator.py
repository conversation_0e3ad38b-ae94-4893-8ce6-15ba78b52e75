#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق حساب معاش المتقاعدين
Pension Calculator Application
"""

import tkinter as tk
from tkinter import ttk, messagebox
import math

class PensionCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("حاسبة معاش المتقاعدين - Pension Calculator")

        # تحسين إعدادات النافذة
        self.root.geometry("900x700")
        self.root.minsize(700, 500)
        self.root.configure(bg='#2c3e50')

        # جعل النافذة قابلة للتكبير
        self.root.resizable(True, True)

        # تحسين DPI
        try:
            self.root.tk.call('tk', 'scaling', 1.2)
        except:
            pass

        # تكوين الخط العربي المحسن
        self.arabic_font = ('Arial Unicode MS', 13)
        self.title_font = ('Arial Unicode MS', 18, 'bold')
        self.result_font = ('Arial Unicode MS', 15, 'bold')

        # ربط أحداث تغيير حجم النافذة
        self.root.bind('<Configure>', self.on_window_resize)

        # ربط مفاتيح اختصار
        self.root.bind('<Control-plus>', lambda e: self.zoom_in())
        self.root.bind('<Control-minus>', lambda e: self.zoom_out())
        self.root.bind('<Control-0>', lambda e: self.reset_zoom())
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())

        self.zoom_level = 1.0
        self.create_widgets()

    def on_window_resize(self, event):
        """تعامل مع تغيير حجم النافذة"""
        if event.widget == self.root:
            width = self.root.winfo_width()

            # تعديل حجم الخط حسب عرض النافذة
            if width > 1200:
                base_font = 14
                title_font = 20
            elif width > 900:
                base_font = 13
                title_font = 18
            else:
                base_font = 12
                title_font = 16

            # تطبيق مستوى التكبير
            base_font = int(base_font * self.zoom_level)
            title_font = int(title_font * self.zoom_level)

            self.arabic_font = ('Arial Unicode MS', base_font)
            self.title_font = ('Arial Unicode MS', title_font, 'bold')
            self.result_font = ('Arial Unicode MS', base_font + 1, 'bold')

    def zoom_in(self):
        """تكبير الواجهة والنافذة"""
        if self.zoom_level < 2.0:
            self.zoom_level += 0.2
            self.update_fonts_and_window()

    def zoom_out(self):
        """تصغير الواجهة والنافذة"""
        if self.zoom_level > 0.6:
            self.zoom_level -= 0.2
            self.update_fonts_and_window()

    def reset_zoom(self):
        """إعادة تعيين مستوى التكبير"""
        self.zoom_level = 1.0
        self.update_fonts_and_window()

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)

        if not current_state:
            self.root.bind('<Escape>', lambda e: self.root.attributes('-fullscreen', False))
        else:
            self.root.unbind('<Escape>')

    def update_fonts_and_window(self):
        """تحديث الخطوط وحجم النافذة بعد تغيير مستوى التكبير"""
        # حساب الحجم الأساسي
        base_width = 900
        base_height = 700
        base_font = 13
        title_font = 18

        # تطبيق مستوى التكبير
        new_width = int(base_width * self.zoom_level)
        new_height = int(base_height * self.zoom_level)
        new_base_font = int(base_font * self.zoom_level)
        new_title_font = int(title_font * self.zoom_level)

        # تحديد الحد الأدنى والأقصى
        new_width = max(700, min(new_width, 1800))
        new_height = max(500, min(new_height, 1200))
        new_base_font = max(8, min(new_base_font, 24))
        new_title_font = max(12, min(new_title_font, 32))

        # تحديث حجم النافذة
        self.root.geometry(f"{new_width}x{new_height}")

        # تحديث الخطوط
        self.arabic_font = ('Arial Unicode MS', new_base_font)
        self.title_font = ('Arial Unicode MS', new_title_font, 'bold')
        self.result_font = ('Arial Unicode MS', new_base_font + 1, 'bold')

        # إعادة إنشاء الواجهة
        self.refresh_widgets()

    def update_fonts(self):
        """تحديث الخطوط فقط (للتوافق مع الكود القديم)"""
        self.update_fonts_and_window()

    def refresh_widgets(self):
        """تحديث الواجهة"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # إعادة إنشاء الواجهة
        self.create_widgets()

    def create_widgets(self):
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#34495e', relief='raised', bd=2)
        title_frame.pack(fill='x', padx=10, pady=10)

        title_label = tk.Label(title_frame, text="الزيادة في معاشات المتقاعدين",
                              font=self.title_font, bg='#34495e', fg='white')
        title_label.pack(pady=10)

        # تعليمات التكبير
        instructions_label = tk.Label(title_frame,
                                    text="Ctrl + تكبير النافذة | Ctrl - تصغير النافذة | Ctrl 0 إعادة تعيين | F11 ملء الشاشة",
                                    font=('Arial Unicode MS', 9), bg='#34495e', fg='#bdc3c7')
        instructions_label.pack(pady=2)

        # معلومات مستوى التكبير
        zoom_info_label = tk.Label(title_frame,
                                 text=f"مستوى التكبير: {self.zoom_level:.1f}x",
                                 font=('Arial Unicode MS', 8), bg='#34495e', fg='#95a5a6')
        zoom_info_label.pack(pady=1)

        # إطار الإدخال
        input_frame = tk.LabelFrame(self.root, text="بيانات الإدخال", font=self.arabic_font,
                                   bg='#ecf0f1', fg='#2c3e50', relief='groove', bd=2)
        input_frame.pack(fill='x', padx=10, pady=5)

        # المعاش الصافي الحالي
        tk.Label(input_frame, text="المعاش الصافي الحالي:", font=self.arabic_font,
                bg='#ecf0f1').grid(row=0, column=1, sticky='e', padx=10, pady=5)
        self.current_pension_var = tk.StringVar(value="40608.00")
        tk.Entry(input_frame, textvariable=self.current_pension_var, font=self.arabic_font,
                width=15, justify='center').grid(row=0, column=0, padx=10, pady=5)

        # نسبة IRG المحسوبة
        tk.Label(input_frame, text="نسبة IRG المحسوبة:", font=self.arabic_font,
                bg='#ecf0f1').grid(row=1, column=1, sticky='e', padx=10, pady=5)
        self.irg_rate_var = tk.StringVar(value="4472.00")
        tk.Entry(input_frame, textvariable=self.irg_rate_var, font=self.arabic_font,
                width=15, justify='center').grid(row=1, column=0, padx=10, pady=5)

        # الخام الخاضع للضريبة
        tk.Label(input_frame, text="الخام الخاضع للضريبة:", font=self.arabic_font,
                bg='#ecf0f1').grid(row=2, column=1, sticky='e', padx=10, pady=5)
        self.taxable_gross_var = tk.StringVar(value="43080.00")
        tk.Entry(input_frame, textvariable=self.taxable_gross_var, font=self.arabic_font,
                width=15, justify='center').grid(row=2, column=0, padx=10, pady=5)

        # CNAS (2%)
        tk.Label(input_frame, text="CNAS (2%):", font=self.arabic_font,
                bg='#ecf0f1').grid(row=3, column=1, sticky='e', padx=10, pady=5)
        self.cnas_var = tk.StringVar(value="920.00")
        tk.Entry(input_frame, textvariable=self.cnas_var, font=self.arabic_font,
                width=15, justify='center').grid(row=3, column=0, padx=10, pady=5)

        # نسبة الزيادة
        tk.Label(input_frame, text="نسبة الزيادة (%):", font=self.arabic_font,
                bg='#ecf0f1').grid(row=4, column=1, sticky='e', padx=10, pady=5)
        self.increase_rate_var = tk.StringVar(value="15")
        tk.Entry(input_frame, textvariable=self.increase_rate_var, font=self.arabic_font,
                width=15, justify='center').grid(row=4, column=0, padx=10, pady=5)

        # أزرار التحكم
        button_frame = tk.Frame(self.root, bg='#2c3e50')
        button_frame.pack(fill='x', padx=10, pady=10)

        calculate_btn = tk.Button(button_frame, text="حساب المعاش الجديد",
                                 font=self.arabic_font, bg='#27ae60', fg='white',
                                 command=self.calculate_pension, relief='raised', bd=3)
        calculate_btn.pack(side='right', padx=5)

        clear_btn = tk.Button(button_frame, text="مسح البيانات",
                             font=self.arabic_font, bg='#e74c3c', fg='white',
                             command=self.clear_data, relief='raised', bd=3)
        clear_btn.pack(side='right', padx=5)

        # إطار النتائج
        self.result_frame = tk.LabelFrame(self.root, text="النتائج", font=self.arabic_font,
                                         bg='#ecf0f1', fg='#2c3e50', relief='groove', bd=2)
        self.result_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إنشاء جدول النتائج
        self.create_results_table()

    def create_results_table(self):
        # إطار للجدول
        table_frame = tk.Frame(self.result_frame, bg='#ecf0f1')
        table_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # عناوين الأعمدة
        headers = ["البيان", "القيمة"]
        for i, header in enumerate(headers):
            label = tk.Label(table_frame, text=header, font=self.result_font,
                           bg='#3498db', fg='white', relief='raised', bd=2)
            label.grid(row=0, column=i, sticky='ew', padx=1, pady=1)

        # صفوف البيانات
        self.result_labels = {}
        result_items = [
            ("الخام قبل التثمين", "gross_before"),
            ("نسبة التثمين", "pension_rate"),
            ("الخام الجديد بعد التثمين", "new_gross"),
            ("CNAS (2%)", "new_cnas"),
            ("الخام الخاضع للضريبة", "new_taxable"),
            ("نسبة IRG المحسوبة", "new_irg"),
            ("المعاش الصافي الجديد", "new_net_pension"),
            ("قيمة الزيادة", "increase_amount"),
            ("النسبة", "increase_percentage")
        ]

        for i, (label_text, key) in enumerate(result_items, 1):
            # عمود البيان
            tk.Label(table_frame, text=label_text, font=self.arabic_font,
                    bg='#bdc3c7', relief='raised', bd=1).grid(row=i, column=0,
                    sticky='ew', padx=1, pady=1)

            # عمود القيمة
            value_label = tk.Label(table_frame, text="0.00", font=self.arabic_font,
                                 bg='white', relief='sunken', bd=1)
            value_label.grid(row=i, column=1, sticky='ew', padx=1, pady=1)
            self.result_labels[key] = value_label

        # تكوين الأعمدة
        table_frame.grid_columnconfigure(0, weight=1)
        table_frame.grid_columnconfigure(1, weight=1)

    def calculate_pension(self):
        try:
            # قراءة البيانات
            current_pension = float(self.current_pension_var.get().replace(',', ''))
            irg_rate = float(self.irg_rate_var.get().replace(',', ''))
            taxable_gross = float(self.taxable_gross_var.get().replace(',', ''))
            cnas = float(self.cnas_var.get().replace(',', ''))
            increase_rate = float(self.increase_rate_var.get())

            # حساب الخام قبل التثمين
            gross_before = current_pension + irg_rate + cnas

            # حساب قيمة الزيادة
            increase_amount = gross_before * (increase_rate / 100)

            # حساب الخام الجديد بعد التثمين
            new_gross = gross_before + increase_amount

            # حساب CNAS الجديد (2%)
            new_cnas = new_gross * 0.02

            # حساب الخام الخاضع للضريبة الجديد
            new_taxable = new_gross - new_cnas

            # حساب IRG الجديد (نفس النسبة من الخام الخاضع للضريبة)
            irg_percentage = irg_rate / taxable_gross if taxable_gross > 0 else 0
            new_irg = new_taxable * irg_percentage

            # حساب المعاش الصافي الجديد
            new_net_pension = new_gross - new_cnas - new_irg

            # تحديث النتائج
            self.result_labels["gross_before"].config(text=f"{gross_before:,.2f}")
            self.result_labels["pension_rate"].config(text=f"{increase_rate}%")
            self.result_labels["new_gross"].config(text=f"{new_gross:,.2f}")
            self.result_labels["new_cnas"].config(text=f"{new_cnas:,.2f}")
            self.result_labels["new_taxable"].config(text=f"{new_taxable:,.2f}")
            self.result_labels["new_irg"].config(text=f"{new_irg:,.2f}")
            self.result_labels["new_net_pension"].config(text=f"{new_net_pension:,.2f}")
            self.result_labels["increase_amount"].config(text=f"{increase_amount:,.2f}")
            self.result_labels["increase_percentage"].config(text=f"{increase_rate}%")

            # تلوين المعاش الجديد
            self.result_labels["new_net_pension"].config(bg='#2ecc71', fg='white', font=self.result_font)
            self.result_labels["increase_amount"].config(bg='#f39c12', fg='white', font=self.result_font)

        except ValueError as e:
            messagebox.showerror("خطأ في البيانات", "يرجى التأكد من صحة البيانات المدخلة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def clear_data(self):
        """مسح جميع البيانات"""
        self.current_pension_var.set("")
        self.irg_rate_var.set("")
        self.taxable_gross_var.set("")
        self.cnas_var.set("")
        self.increase_rate_var.set("")

        # مسح النتائج
        for label in self.result_labels.values():
            label.config(text="0.00", bg='white', fg='black', font=self.arabic_font)

def main():
    root = tk.Tk()
    app = PensionCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
