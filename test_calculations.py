#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حسابات معاش المتقاعدين
Test Pension Calculations
"""

def test_pension_calculations():
    """اختبار الحسابات مع البيانات من الصورة"""
    
    # البيانات من الصورة
    current_pension = 40608.00
    irg_rate = 4472.00
    taxable_gross = 43080.00
    cnas = 920.00
    increase_rate = 15  # %
    
    print("=" * 60)
    print("اختبار حسابات معاش المتقاعدين")
    print("Testing Pension Calculations")
    print("=" * 60)
    
    print(f"\nالبيانات الأساسية:")
    print(f"المعاش الصافي الحالي: {current_pension:,.2f} دج")
    print(f"نسبة IRG المحسوبة: {irg_rate:,.2f} دج")
    print(f"الخام الخاضع للضريبة: {taxable_gross:,.2f} دج")
    print(f"CNAS (2%): {cnas:,.2f} دج")
    print(f"نسبة الزيادة: {increase_rate}%")
    
    # الحسابات
    print(f"\nالحسابات:")
    
    # 1. الخام قبل التثمين
    gross_before = current_pension + irg_rate + cnas
    print(f"1. الخام قبل التثمين = {current_pension:,.2f} + {irg_rate:,.2f} + {cnas:,.2f} = {gross_before:,.2f} دج")
    
    # 2. قيمة الزيادة
    increase_amount = gross_before * (increase_rate / 100)
    print(f"2. قيمة الزيادة = {gross_before:,.2f} × {increase_rate}% = {increase_amount:,.2f} دج")
    
    # 3. الخام الجديد بعد التثمين
    new_gross = gross_before + increase_amount
    print(f"3. الخام الجديد = {gross_before:,.2f} + {increase_amount:,.2f} = {new_gross:,.2f} دج")
    
    # 4. CNAS الجديد (2%)
    new_cnas = new_gross * 0.02
    print(f"4. CNAS الجديد = {new_gross:,.2f} × 2% = {new_cnas:,.2f} دج")
    
    # 5. الخام الخاضع للضريبة الجديد
    new_taxable = new_gross - new_cnas
    print(f"5. الخام الخاضع للضريبة الجديد = {new_gross:,.2f} - {new_cnas:,.2f} = {new_taxable:,.2f} دج")
    
    # 6. نسبة IRG
    irg_percentage = irg_rate / taxable_gross if taxable_gross > 0 else 0
    print(f"6. نسبة IRG = {irg_rate:,.2f} ÷ {taxable_gross:,.2f} = {irg_percentage:.4f} ({irg_percentage*100:.2f}%)")
    
    # 7. IRG الجديد
    new_irg = new_taxable * irg_percentage
    print(f"7. IRG الجديد = {new_taxable:,.2f} × {irg_percentage:.4f} = {new_irg:,.2f} دج")
    
    # 8. المعاش الصافي الجديد
    new_net_pension = new_gross - new_cnas - new_irg
    print(f"8. المعاش الصافي الجديد = {new_gross:,.2f} - {new_cnas:,.2f} - {new_irg:,.2f} = {new_net_pension:,.2f} دج")
    
    # النتائج النهائية
    print(f"\n" + "=" * 60)
    print("النتائج النهائية:")
    print("=" * 60)
    
    net_increase = new_net_pension - current_pension
    effective_rate = (net_increase / current_pension) * 100
    
    print(f"المعاش الصافي الحالي: {current_pension:,.2f} دج")
    print(f"المعاش الصافي الجديد: {new_net_pension:,.2f} دج")
    print(f"الزيادة الصافية: {net_increase:,.2f} دج")
    print(f"النسبة الفعلية للزيادة: {effective_rate:.2f}%")
    
    # مقارنة مع البيانات المتوقعة من الصورة
    print(f"\n" + "=" * 60)
    print("مقارنة مع البيانات المتوقعة:")
    print("=" * 60)
    
    expected_new_pension = 45545.00  # من الصورة
    expected_increase = 4937.00      # من الصورة
    
    print(f"المعاش الجديد المتوقع: {expected_new_pension:,.2f} دج")
    print(f"المعاش الجديد المحسوب: {new_net_pension:,.2f} دج")
    print(f"الفرق: {abs(new_net_pension - expected_new_pension):,.2f} دج")
    
    print(f"\nالزيادة المتوقعة: {expected_increase:,.2f} دج")
    print(f"الزيادة المحسوبة: {net_increase:,.2f} دج")
    print(f"الفرق: {abs(net_increase - expected_increase):,.2f} دج")
    
    # التحقق من دقة الحسابات
    pension_diff = abs(new_net_pension - expected_new_pension)
    increase_diff = abs(net_increase - expected_increase)
    
    if pension_diff < 1.0 and increase_diff < 1.0:
        print(f"\n✅ الحسابات صحيحة! الفرق أقل من 1 دج")
    else:
        print(f"\n⚠️ يوجد اختلاف في الحسابات")
    
    return {
        'current_pension': current_pension,
        'new_pension': new_net_pension,
        'increase': net_increase,
        'effective_rate': effective_rate,
        'calculations': {
            'gross_before': gross_before,
            'increase_amount': increase_amount,
            'new_gross': new_gross,
            'new_cnas': new_cnas,
            'new_taxable': new_taxable,
            'new_irg': new_irg
        }
    }

def test_different_scenarios():
    """اختبار سيناريوهات مختلفة"""
    
    print(f"\n" + "=" * 60)
    print("اختبار سيناريوهات مختلفة:")
    print("=" * 60)
    
    scenarios = [
        {"name": "زيادة 10%", "rate": 10},
        {"name": "زيادة 20%", "rate": 20},
        {"name": "زيادة 25%", "rate": 25},
        {"name": "زيادة 30%", "rate": 30}
    ]
    
    base_pension = 40608.00
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        
        # حساب سريع
        gross_before = 46000.00  # تقريبي
        increase_amount = gross_before * (scenario['rate'] / 100)
        new_gross = gross_before + increase_amount
        new_net_approx = new_gross * 0.88  # تقريبي بعد خصم الضرائب
        
        net_increase = new_net_approx - base_pension
        
        print(f"  المعاش الجديد التقريبي: {new_net_approx:,.2f} دج")
        print(f"  الزيادة التقريبية: {net_increase:,.2f} دج")

if __name__ == "__main__":
    # تشغيل الاختبارات
    results = test_pension_calculations()
    test_different_scenarios()
    
    print(f"\n" + "=" * 60)
    print("انتهى الاختبار")
    print("=" * 60)
